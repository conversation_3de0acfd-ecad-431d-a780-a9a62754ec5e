# 外交新闻稿情感时序分析系统

## 项目简介

本项目是一个完整的Python脚本系统，用于对外交新闻稿进行基于Ekman六类情感理论和Valence-Arousal二维情感模型的情感分析。

## 功能特性

- **数据处理**: 自动读取CSV文件，按时间范围筛选数据
- **情感分析**: 调用通义千问API进行Ekman六类情感和VA值分析
- **并发处理**: 提供异步和同步两种版本，支持高效批量处理
- **错误处理**: 完善的异常处理和重试机制
- **日志记录**: 详细的处理日志，便于监控和调试
- **结果保存**: 自动保存分析结果到新的CSV文件

## 安装依赖

### 方法1: 使用pip安装
```bash
pip install -r requirements.txt
```

### 方法2: 手动安装
```bash
pip install pandas requests aiohttp plotly prophet
```

### Prophet安装注意事项
如果Prophet安装失败，请尝试：

**Windows用户:**
```bash
# 先安装依赖
pip install pystan
pip install prophet
```

**macOS用户:**
```bash
# 使用conda安装（推荐）
conda install -c conda-forge prophet
```

**Linux用户:**
```bash
# 安装系统依赖
sudo apt-get install python3-dev
pip install prophet
```

## 文件结构

```
项目目录/
├── emotion_analysis.py          # 主脚本（异步版本）
├── emotion_analysis_sync.py     # 同步版本脚本
├── requirements.txt             # 依赖列表
├── api_key.txt                 # API密钥文件
├── 外交部-德国 .csv            # 输入数据文件
├── 外交部-德国_analyzed.csv    # 输出结果文件（运行后生成）
├── emotion_analysis.log        # 日志文件（运行后生成）
└── README.md                   # 本说明文件
```

## 配置说明

### 1. API密钥配置
确保 `api_key.txt` 文件包含有效的通义千问API密钥：
```
api_key=your_api_key_here
```

### 2. 参数配置
在脚本中可以修改以下参数：

```python
# 文件路径
INPUT_CSV_PATH = '外交部-德国 .csv'
OUTPUT_CSV_PATH = '外交部-德国_analyzed.csv'

# 时间范围
START_DATE = '2023-01-01'  # 开始日期
END_DATE = '2025-12-31'    # 结束日期

# API配置
REQUEST_DELAY = 1          # 请求间隔（秒）
MAX_RETRIES = 3           # 最大重试次数
TIMEOUT = 30              # 请求超时时间（秒）
```

## 使用方法

### 快速启动（推荐）
```bash
python run_analysis.py
```
然后根据提示选择要运行的脚本版本。

### 直接运行脚本

#### 1. 测试版本（推荐新用户）
```bash
python test_emotion_analysis.py
```
仅处理前3条数据，用于验证API连接和功能。

#### 2. 完整版本（推荐）
```bash
python final_emotion_analysis.py
```
处理所有数据，包含完整的错误处理和进度显示。

#### 3. 同步版本
```bash
python emotion_analysis_sync.py
```
同步处理版本，更稳定但速度较慢。

#### 4. 异步版本
```bash
python emotion_analysis.py
```
异步处理版本，速度更快但需要aiohttp依赖。

#### 5. 环境测试
```bash
python simple_test.py
```
检查环境配置和文件是否正常。

## 输出结果

脚本会在原始数据基础上添加以下8个新列：

### Ekman六类情感（概率值，总和为1.0）
- `anger`: 愤怒
- `disgust`: 厌恶  
- `fear`: 恐惧
- `joy`: 喜悦
- `sadness`: 悲伤
- `surprise`: 惊讶

### Valence-Arousal模型
- `valence`: 效价值（-1.0到1.0，负值表示消极，正值表示积极）
- `arousal`: 唤醒度（0.0到1.0，数值越高表示情感强度越大）

## 日志监控

程序运行时会生成详细的日志文件 `emotion_analysis.log`，包含：
- 数据加载和预处理信息
- API调用状态和错误信息
- 处理进度和统计信息
- 异常和重试记录

## 性能优化

### 异步版本优势
- 支持并发API调用，处理速度更快
- 更好的资源利用率
- 适合大批量数据处理

### 同步版本优势
- 代码简单，易于理解和调试
- 兼容性更好
- 适合小批量数据或测试使用

## 错误处理

脚本包含完善的错误处理机制：
- API调用失败自动重试
- 网络超时处理
- JSON解析错误处理
- 数据格式验证
- 空值和异常数据处理

## 注意事项

1. **API限制**: 请注意通义千问API的调用频率限制，脚本已内置延时机制
2. **数据格式**: 确保输入CSV文件包含 `title`、`content`、`date2` 列
3. **内存使用**: 大文件处理时注意内存使用情况
4. **网络连接**: 确保网络连接稳定，API调用需要访问外网

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `api_key.txt` 文件格式
   - 确认API密钥有效性

2. **依赖安装失败**
   - 尝试升级pip: `pip install --upgrade pip`
   - 使用conda安装Prophet

3. **CSV文件读取错误**
   - 检查文件编码（应为UTF-8）
   - 确认文件路径正确

4. **API调用失败**
   - 检查网络连接
   - 确认API服务状态
   - 查看日志文件获取详细错误信息

## 技术支持

如遇到问题，请查看：
1. 日志文件 `emotion_analysis.log`
2. 控制台输出信息
3. 检查网络和API服务状态
