#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外交新闻稿情感分析 - 快速启动脚本
"""

import os
import sys

def main():
    print("=" * 60)
    print("外交新闻稿情感时序分析系统")
    print("=" * 60)
    
    print("\n可用的脚本选项:")
    print("1. final_emotion_analysis.py - 完整版本（推荐）")
    print("2. emotion_analysis_sync.py - 同步版本")
    print("3. test_emotion_analysis.py - 测试版本（仅处理前3条）")
    print("4. simple_test.py - 环境测试")
    
    while True:
        choice = input("\n请选择要运行的脚本 (1-4): ").strip()
        
        if choice == '1':
            script = 'final_emotion_analysis.py'
            break
        elif choice == '2':
            script = 'emotion_analysis_sync.py'
            break
        elif choice == '3':
            script = 'test_emotion_analysis.py'
            break
        elif choice == '4':
            script = 'simple_test.py'
            break
        else:
            print("无效选择，请输入 1-4")
    
    print(f"\n正在运行: {script}")
    print("=" * 60)
    
    # 运行选择的脚本
    os.system(f'python {script}')

if __name__ == "__main__":
    main()
