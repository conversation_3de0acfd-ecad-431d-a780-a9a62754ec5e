#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外交新闻稿情感分析测试脚本
"""

import pandas as pd
import os
import json
import requests
import time

# 配置
INPUT_CSV_PATH = '外交部-德国 .csv'
OUTPUT_CSV_PATH = '外交部-德国_analyzed.csv'
API_KEY_FILE = 'api_key.txt'
START_DATE = '2023-01-01'
END_DATE = '2025-12-31'

# API配置
API_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
MODEL_NAME = "qwen-plus"

def load_api_key():
    """加载API密钥"""
    try:
        with open(API_KEY_FILE, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if '=' in content:
            api_key = content.split('=', 1)[1].strip()
        else:
            api_key = content
            
        print(f"API密钥加载成功: {api_key[:10]}...")
        return api_key
    except Exception as e:
        print(f"加载API密钥失败: {e}")
        return None

def load_data():
    """加载和预处理数据"""
    try:
        print(f"正在加载数据: {INPUT_CSV_PATH}")
        df = pd.read_csv(INPUT_CSV_PATH, encoding='utf-8')
        print(f"原始数据行数: {len(df)}")
        
        # 处理日期
        df['date2_processed'] = pd.to_datetime(df['date2'], format='%Y%m', errors='coerce')
        df = df.dropna(subset=['date2_processed'])
        
        # 时间筛选
        start_date = pd.to_datetime(START_DATE)
        end_date = pd.to_datetime(END_DATE)
        df_filtered = df[(df['date2_processed'] >= start_date) & (df['date2_processed'] <= end_date)].copy()
        
        print(f"筛选后数据行数: {len(df_filtered)}")
        
        # 合并文本
        df_filtered['full_text'] = df_filtered['title'].fillna('') + '\n\n' + df_filtered['content'].fillna('')
        df_filtered = df_filtered[df_filtered['full_text'].str.strip() != ''].copy()
        
        print(f"最终数据行数: {len(df_filtered)}")
        return df_filtered
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def get_emotion_analysis(text, api_key):
    """情感分析函数"""
    if len(text) > 8000:
        text = text[:8000] + "..."
    
    system_message = """你是一名专攻外交话语体系的情感分析专家。你的任务是基于 Ekman 六维情感理论和 Valence-Arousal 二维情感模型，对输入的外交新闻稿进行分析。

请严格按照以下要求执行：

1. **Ekman 六类情感**: 分析文本中包含的"愤怒(anger)"、"厌恶(disgust)"、"恐惧(fear)"、"喜悦(joy)"、"悲伤(sadness)"、"惊讶(surprise)"六种情感的强度。以概率形式表示，总和应为 1.0。

2. **Valence-Arousal 模型**:
   - **效价 (Valence)**: 评估文本表达情感的积极或消极程度，范围从 -1.0 (最负面) 到 1.0 (最正面)。
   - **唤醒度 (Arousal)**: 评估文本表达情感的强度或激动程度，范围从 0.0 (平静) 到 1.0 (激动)。

3. **输出格式**: 你的回答必须是一个且仅一个格式正确的 JSON 对象，不包含任何其他解释性文字。JSON 结构如下：
{
  "anger": <float>,
  "disgust": <float>,
  "fear": <float>,
  "joy": <float>,
  "sadness": <float>,
  "surprise": <float>,
  "valence": <float>,
  "arousal": <float>
}"""
    
    user_message = f"请对以下新闻稿内容进行情感分析，并返回指定的JSON对象：\n\n{text}"
    
    payload = {
        "model": MODEL_NAME,
        "messages": [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ],
        "temperature": 0.2,
        "response_format": {"type": "json_object"}
    }
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/chat/completions",
            json=payload,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            emotion_data = json.loads(content)
            
            # 验证数据格式
            expected_keys = ['anger', 'disgust', 'fear', 'joy', 'sadness', 'surprise', 'valence', 'arousal']
            if all(key in emotion_data for key in expected_keys):
                return emotion_data
            else:
                print("API返回格式不正确")
                return None
        else:
            print(f"API请求失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"API调用异常: {e}")
        return None

def main():
    """主函数"""
    print("=" * 50)
    print("外交新闻稿情感分析测试")
    print("=" * 50)
    
    # 加载API密钥
    api_key = load_api_key()
    if not api_key:
        return
    
    # 加载数据
    df = load_data()
    if df is None or len(df) == 0:
        print("没有可处理的数据")
        return
    
    # 测试处理前3条数据
    test_count = min(3, len(df))
    print(f"\n开始测试处理前 {test_count} 条数据...")
    
    # 初始化结果列
    emotion_columns = ['anger', 'disgust', 'fear', 'joy', 'sadness', 'surprise', 'valence', 'arousal']
    for col in emotion_columns:
        df[col] = None
    
    success_count = 0
    
    for i in range(test_count):
        row = df.iloc[i]
        print(f"\n处理第 {i+1} 条: {row['title'][:50]}...")
        
        result = get_emotion_analysis(row['full_text'], api_key)
        
        if result:
            for col in emotion_columns:
                df.iloc[i, df.columns.get_loc(col)] = result.get(col)
            
            print("分析结果:")
            for col in emotion_columns:
                print(f"  {col}: {result.get(col, 'N/A')}")
            
            success_count += 1
        else:
            print("  分析失败")
        
        # 延时
        if i < test_count - 1:
            time.sleep(1)
    
    print(f"\n测试完成！成功分析 {success_count}/{test_count} 条数据")
    
    # 保存结果
    if success_count > 0:
        df.to_csv(OUTPUT_CSV_PATH, index=False, encoding='utf-8')
        print(f"结果已保存到: {OUTPUT_CSV_PATH}")

if __name__ == "__main__":
    main()
