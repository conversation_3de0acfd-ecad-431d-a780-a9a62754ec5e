#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试索引修复是否正确
"""

import pandas as pd

# 模拟数据加载和筛选过程
print("测试索引修复...")

# 创建模拟数据
data = {
    'title': [f'新闻{i}' for i in range(1, 11)],
    'date2': ['202301', '202302', '202303', '202304', '202305', 
              '202306', '202307', '202308', '202309', '202310']
}
df = pd.DataFrame(data)
print(f"原始数据: {len(df)} 行")
print(f"原始索引: {list(df.index)}")

# 模拟时间筛选（只保留部分数据）
df_filtered = df[df['date2'].isin(['202303', '202305', '202307'])].copy()
print(f"\n筛选后数据: {len(df_filtered)} 行")
print(f"筛选后索引: {list(df_filtered.index)}")

total_count = len(df_filtered)

print(f"\n=== 修复前的问题演示 ===")
for idx, row in df_filtered.iterrows():
    current_num = idx + 1  # 这里会有问题
    print(f"处理第 {current_num} / {total_count} 条: {row['title']}")
    if current_num >= 3:  # 只演示前几个
        break

print(f"\n=== 修复后的正确方式 ===")
for current_idx, (original_idx, row) in enumerate(df_filtered.iterrows()):
    current_num = current_idx + 1  # 这样是正确的
    print(f"处理第 {current_num} / {total_count} 条: {row['title']} (原始索引: {original_idx})")

print("\n修复验证完成！")
