#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外交新闻稿情感时序分析系统 - 最终版本
作者: AI数据科学家
功能: 对外交新闻稿进行Ekman六类情感和Valence-Arousal情感分析
"""

import pandas as pd
import os
import json
import time
import requests
from datetime import datetime
from typing import Dict, Optional
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('emotion_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ==================== 常量与参数配置 ====================
INPUT_CSV_PATH = '外交部-德国 .csv'
OUTPUT_CSV_PATH = '外交部-德国_analyzed.csv'
API_KEY_FILE = 'api_key.txt'
START_DATE = '2023-01-01'  # 可配置
END_DATE = '2025-12-31'    # 可配置

# API配置
API_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
MODEL_NAME = "qwen-plus"
REQUEST_DELAY = 1.5  # 请求间隔（秒）
MAX_RETRIES = 3      # 最大重试次数
TIMEOUT = 30         # 请求超时时间（秒）

# ==================== 工具函数 ====================

def check_dependencies():
    """检查必要的依赖"""
    required_modules = ['pandas', 'requests']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✓ {module} 可用")
        except ImportError:
            missing_modules.append(module)
            logger.error(f"✗ {module} 未安装")
    
    if missing_modules:
        logger.error(f"请安装缺失的模块: pip install {' '.join(missing_modules)}")
        return False
    return True

def load_api_key() -> str:
    """从API_KEY_FILE文件中安全地读取API Key"""
    try:
        if not os.path.exists(API_KEY_FILE):
            raise FileNotFoundError(f"API密钥文件 {API_KEY_FILE} 不存在")
        
        with open(API_KEY_FILE, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            
        # 支持多种格式：api_key=xxx 或直接是密钥
        if '=' in content:
            api_key = content.split('=', 1)[1].strip()
        else:
            api_key = content
            
        if not api_key:
            raise ValueError("API密钥为空")
            
        logger.info(f"API密钥加载成功: {api_key[:10]}...")
        return api_key
        
    except Exception as e:
        logger.error(f"加载API密钥失败: {e}")
        raise

def load_and_preprocess_data() -> pd.DataFrame:
    """加载并预处理CSV数据"""
    try:
        logger.info(f"正在加载数据文件: {INPUT_CSV_PATH}")
        
        # 读取CSV文件
        df = pd.read_csv(INPUT_CSV_PATH, encoding='utf-8')
        logger.info(f"原始数据行数: {len(df)}")
        
        # 检查必要的列是否存在
        required_columns = ['title', 'content', 'date2']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"缺少必要的列: {missing_columns}")
        
        # 处理date2列，转换为datetime对象
        logger.info("正在处理日期数据...")
        df['date2_processed'] = pd.to_datetime(df['date2'], format='%Y%m', errors='coerce')
        
        # 移除日期转换失败的行
        invalid_dates = df['date2_processed'].isna().sum()
        if invalid_dates > 0:
            logger.warning(f"发现 {invalid_dates} 行日期格式无效，将被跳过")
            df = df.dropna(subset=['date2_processed'])
        
        # 根据时间范围筛选数据
        start_date = pd.to_datetime(START_DATE)
        end_date = pd.to_datetime(END_DATE)
        
        df_filtered = df[
            (df['date2_processed'] >= start_date) & 
            (df['date2_processed'] <= end_date)
        ].copy()
        
        logger.info(f"时间筛选后数据行数: {len(df_filtered)} (时间范围: {START_DATE} 到 {END_DATE})")
        
        # 合并title和content为full_text
        logger.info("正在合并标题和内容...")
        df_filtered['full_text'] = df_filtered['title'].fillna('') + '\n\n' + df_filtered['content'].fillna('')
        
        # 移除空文本的行
        df_filtered = df_filtered[df_filtered['full_text'].str.strip() != ''].copy()
        logger.info(f"移除空文本后数据行数: {len(df_filtered)}")
        
        return df_filtered
        
    except Exception as e:
        logger.error(f"数据加载和预处理失败: {e}")
        raise

def get_emotion_analysis(text: str, api_key: str) -> Dict[str, Optional[float]]:
    """对单个文本进行情感分析"""
    # 默认返回值（用于错误情况）
    default_result = {
        'anger': None, 'disgust': None, 'fear': None, 'joy': None,
        'sadness': None, 'surprise': None, 'valence': None, 'arousal': None
    }
    
    if not text or not text.strip():
        logger.warning("输入文本为空")
        return default_result
    
    # 截断过长的文本（避免API限制）
    # if len(text) > 8000:
    #     text = text[:8000] + "..."
    #     logger.warning("文本过长，已截断")
    
    # 构建提示词
    system_message = """你是一名专攻外交话语体系的情感分析专家。你的任务是基于 Ekman 六维情感理论和 Valence-Arousal 二维情感模型，对输入的外交新闻稿进行分析。

请严格按照以下要求执行：

1. **Ekman 六类情感**: 分析文本中包含的"愤怒(anger)"、"厌恶(disgust)"、"恐惧(fear)"、"喜悦(joy)"、"悲伤(sadness)"、"惊讶(surprise)"六种情感的强度。以概率形式表示，总和应为 1.0。

2. **Valence-Arousal 模型**:
   - **效价 (Valence)**: 评估文本表达情感的积极或消极程度，范围从 -1.0 (最负面) 到 1.0 (最正面)。
   - **唤醒度 (Arousal)**: 评估文本表达情感的强度或激动程度，范围从 0.0 (平静) 到 1.0 (激动)。

3. **输出格式**: 你的回答必须是一个且仅一个格式正确的 JSON 对象，不包含任何其他解释性文字。JSON 结构如下：
{
  "anger": <float>,
  "disgust": <float>,
  "fear": <float>,
  "joy": <float>,
  "sadness": <float>,
  "surprise": <float>,
  "valence": <float>,
  "arousal": <float>
}"""
    
    user_message = f"请对以下新闻稿内容进行情感分析，并返回指定的JSON对象：\n\n{text}"
    
    messages = [
        {"role": "system", "content": system_message},
        {"role": "user", "content": user_message}
    ]
    
    payload = {
        "model": MODEL_NAME,
        "messages": messages,
        "temperature": 0.2,
        "response_format": {"type": "json_object"}
    }
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    for attempt in range(MAX_RETRIES):
        try:
            response = requests.post(
                f"{API_BASE_URL}/chat/completions",
                json=payload,
                headers=headers,
                timeout=TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 解析JSON响应
                emotion_data = json.loads(content)
                
                # 验证返回的数据格式
                expected_keys = ['anger', 'disgust', 'fear', 'joy', 'sadness', 'surprise', 'valence', 'arousal']
                if all(key in emotion_data for key in expected_keys):
                    # 确保所有值都是数字类型
                    for key in expected_keys:
                        if not isinstance(emotion_data[key], (int, float)):
                            emotion_data[key] = None
                    return emotion_data
                else:
                    logger.error(f"API返回的JSON格式不正确，缺少必要字段")
                    
            else:
                logger.error(f"API请求失败 (状态码: {response.status_code}): {response.text}")
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
        except requests.exceptions.Timeout:
            logger.error(f"请求超时 (尝试 {attempt + 1}/{MAX_RETRIES})")
        except Exception as e:
            logger.error(f"API调用异常 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
        
        # 如果不是最后一次尝试，等待后重试
        if attempt < MAX_RETRIES - 1:
            time.sleep(REQUEST_DELAY * (attempt + 1))
    
    logger.error("所有重试均失败，返回默认值")
    return default_result

def main():
    """主函数"""
    try:
        logger.info("=" * 60)
        logger.info("开始外交新闻稿情感分析")
        logger.info("=" * 60)
        
        # 检查依赖
        if not check_dependencies():
            return
        
        # 第一步：环境设置与数据加载
        logger.info("第一步：加载API密钥和数据...")
        api_key = load_api_key()
        df = load_and_preprocess_data()
        
        if len(df) == 0:
            logger.warning("没有符合条件的数据需要处理")
            return
        
        # 询问用户是否要处理所有数据
        total_count = len(df)
        logger.info(f"共有 {total_count} 条数据需要处理")
        
        # 第二步：批量情感分析
        logger.info(f"第二步：开始批量情感分析...")
        
        # 初始化结果列
        emotion_columns = ['anger', 'disgust', 'fear', 'joy', 'sadness', 'surprise', 'valence', 'arousal']
        for col in emotion_columns:
            df[col] = None
        
        success_count = 0
        start_time = time.time()
        
        # 逐条处理新闻
        for idx, row in df.iterrows():
            current_num = idx + 1
            logger.info(f"正在处理第 {current_num} / {total_count} 条新闻: {row['title'][:50]}...")
            
            result = get_emotion_analysis(row['full_text'], api_key)
            
            # 将结果写入DataFrame
            for col in emotion_columns:
                df.at[idx, col] = result[col]
            
            if result['anger'] is not None:
                success_count += 1
                logger.info(f"  ✓ 分析成功 (成功率: {success_count}/{current_num})")
            else:
                logger.warning(f"  ✗ 分析失败")
            
            # # 显示预估剩余时间
            # if current_num > 1:
            #     elapsed_time = time.time() - start_time
            #     avg_time_per_item = elapsed_time / current_num
            #     remaining_items = total_count - current_num
            #     estimated_remaining_time = remaining_items * avg_time_per_item
            #     logger.info(f"  预估剩余时间: {estimated_remaining_time/60:.1f} 分钟")
            
            # # 添加延时避免API限制
            # if idx < len(df) - 1:  # 不是最后一个
            #     time.sleep(REQUEST_DELAY)
        
        # 第三步：保存结果
        logger.info("第三步：保存结果...")
        df.to_csv(OUTPUT_CSV_PATH, index=False, encoding='utf-8')
        logger.info(f"结果已保存到: {OUTPUT_CSV_PATH}")
        
        # 统计信息
        total_time = time.time() - start_time
        logger.info(f"处理完成！总耗时: {total_time/60:.1f} 分钟")
        logger.info(f"成功分析: {success_count}/{total_count} 条新闻 (成功率: {success_count/total_count*100:.1f}%)")
        
        # 显示样本结果
        logger.info("\n样本分析结果:")
        for i in range(min(3, len(df))):
            logger.info(f"新闻 {i+1}: {df.iloc[i]['title'][:50]}...")
            for col in emotion_columns:
                value = df.iloc[i][col]
                if value is not None:
                    logger.info(f"  {col}: {value:.4f}")
                else:
                    logger.info(f"  {col}: 分析失败")
        
        logger.info("=" * 60)
        logger.info("情感分析完成！")
        logger.info("=" * 60)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise

if __name__ == "__main__":
    main()
