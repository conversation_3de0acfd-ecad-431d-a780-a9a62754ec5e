#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("开始测试...")

try:
    import pandas as pd
    print("✓ pandas 导入成功")
except ImportError as e:
    print(f"✗ pandas 导入失败: {e}")

try:
    import requests
    print("✓ requests 导入成功")
except ImportError as e:
    print(f"✗ requests 导入失败: {e}")

try:
    import os
    print("✓ os 导入成功")
except ImportError as e:
    print(f"✗ os 导入失败: {e}")

# 检查文件
files_to_check = ['外交部-德国 .csv', 'api_key.txt']
for file in files_to_check:
    if os.path.exists(file):
        print(f"✓ 文件存在: {file}")
    else:
        print(f"✗ 文件不存在: {file}")

# 测试CSV读取
try:
    df = pd.read_csv('外交部-德国 .csv', encoding='utf-8')
    print(f"✓ CSV读取成功，行数: {len(df)}")
    print(f"✓ 列名: {list(df.columns)}")
except Exception as e:
    print(f"✗ CSV读取失败: {e}")

# 测试API密钥读取
try:
    with open('api_key.txt', 'r', encoding='utf-8') as f:
        content = f.read().strip()
    if '=' in content:
        api_key = content.split('=', 1)[1].strip()
    else:
        api_key = content
    print(f"✓ API密钥读取成功: {api_key[:10]}...")
except Exception as e:
    print(f"✗ API密钥读取失败: {e}")

print("测试完成！")
